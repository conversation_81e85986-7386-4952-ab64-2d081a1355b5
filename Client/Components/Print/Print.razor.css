.print-main {
    min-height: 1000px;
    height: auto;
    background-color: #fff;
    padding-bottom: 250px;
}
 
.print-header {
    position: relative;
    top: 200px;
    padding-left: 150px;
}
 
.print-header-buttons {
    position: relative;
    float: right;
    right: 110px;
    top: -55px;
}
  
.button-style {
    cursor: pointer;
    margin-right: 20px;
    font-size: 22px;
    background: #CA2F35;
    border: 1px solid #CA2F35;
    border-radius: 35px;
    color: white;
    font-family: Raleway;
    padding: 8px 20px;
}

#print-report {
    position: relative;
    top: 18px;
}

.print-section {
    position: relative;
    top: 195px;
    left: 180px;
    width: calc(100vw - 20%);
    min-height: 800px;
    height: 100%;
    overflow-y: hidden;
    background-color: #fff;
    box-shadow: 0px 4px 65px 0px rgba(0, 0, 0, 0.16);
    border-radius: 8px;
    padding-bottom: 200px;
}

.chk-print {
    display: flex;
    flex-direction: row;
    position: relative;
    text-align: left;
    font-size: 18px;
    margin-left: 20px;
}

.chk-wrap {
    font-family: Raleway;
    padding: 60px 18px 0 22px;
}

.chk-select-all {
    position: absolute;
    font-family: Raleway;
    top: 5%;
    right: 74.3px;
}

.print-recipe {
    position: relative;
    top: 120px;
    padding-left: 40px;
}

.rec-data {
    display: flex;
    flex-direction: row;
}

.chk-select-print {
    font-family: Raleway;
    position: relative;
    top: -30px;
    right: 74px;
    float: right;
}

.print-bar {
    position: relative;
    top: -20px;
    left: 1px;
    width: calc(100vw - 32%);
}

/*print data*/

.print-preview {
    min-height: 1000px;
    height: auto;
    background-color: #fff;
    padding-bottom: 250px;
    overflow-x: hidden;
    visibility: visible;
}

.meal-plan-print {
    position: relative;
    padding-left: 130px;
    padding-right: 50px;
    padding-top: 130px;
}
.print-shopping {
    position: relative;
    padding-left: 130px;
    padding-right: 50px;
    page-break-before: always;
}
.print-prep-guide {
    position: relative;
    padding-left: 130px;
    padding-right: 50px;
    page-break-before: always;
}
.print-recipe-sec {
    position: relative;
    padding-left: 130px;
    padding-right: 50px;
    page-break-before: always;
}
.text-float {
    font-family: 'Times New Roman';
    font-size: 28px;
    color: darkolivegreen;
    float: right;
    position: relative;
    top: -28px;
    right: 120px;
}

.recipe-holder{    
    width: calc(100% - 7%);
    border: 1px solid grey;
    /*height: 70px;*/
    padding: 25px 0 1px 0;
}
.print-diet-name {
    padding: 4px 0 2px 8px;
    margin: 0 auto;
    font-family: Raleway;
}
.print-recipe-name {
    padding: 5px 0px 2px 8px;
    font-family: Raleway;
}

.img-logo{
    position:relative;
    top: 17px;
}
.img-logo img {
    height: 90px;    
}
.btn-loc{
    float: right;
    position: relative;
    right: 100px;
    top: 136px;
    z-index: 1000;
}

.text-float-prep {
    font-family: 'Times New Roman';
    font-size: 26px;
    color: darkolivegreen;
    float: right;
    position:absolute;
    top: 60px;
    right: 95px;
}

.billing-hist-header {
    position: relative;
    display: flex;
    font-family: 'Raleway';
    justify-content: space-between;
    background: rgba(229, 223, 195, 0.7);
    border-radius: 10px 10px 0px 0px;
    padding: 25px 50px 0px 0px;
    width: 100%;
    height: 70px;
    font-weight: 500;
}

.prep-table {
    position: relative;
    width: calc(43.5vw - 100px);
    height: auto;
    background: #FFFFFF;   
    border-radius: 11px;
    border: 1px solid grey;
    font-style: normal;
    font-weight: 700;
    font-size: 18px;
    line-height: 21px;
    padding-bottom: 0px;
    margin-bottom: 100px;
}

.prep-table-data {
    padding: 10px 0 8px 0;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    font-family: 'Raleway';
    font-style: normal;
    font-weight: 400;
    font-size: 15px;
    line-height: 22px;
}

    .prep-table-data:nth-child(odd) {
        background: rgba(229, 223, 195, 0.258);
    }

.prep-instructions {
    width: 500px;
    text-align: left;
    padding: 0 20px;
}

.prep-recipe {
    width: 300px;
    text-align: left;
    padding-right: 10px;
}

.prep-done {
    width: 50px;
    text-align: left;
}

.header-item-200 {
    width: 200px;
    text-align: center;
}

.header-item-250 {
    width: 250px;
    text-align: left;
    padding-left: 30px;
}

.header-item-80 {
    width: 70px;
    text-align: right;
    padding-left: 50px;
}

/*recipe-detail-section*/
.recipe {
    page-break-after: always;
}

.see-recipe-name {
    font-family: playfair;
    font-size: 20px;
}

.group-pill {
    padding: 1px 8px 4px 8px;
    background: #F1F1F1;
    /*background: blue;*/
    border-radius: 10px;
}

.see-header {
    margin-top: 25px;
}

    .see-recipe-button:hover {
        background-color: rgba(202, 47, 53, 1);
        color: white;
    }

.ing-style {
    font-family: Raleway;
    width: 300px !important;
}

.see-recipe-ing {
    font-weight: 500;
    font-size: 16px;
    padding: 20px 0;
}

.see-recipe-ins {
    font-weight: 500;
    font-size: 16px;
    margin: 30px 0 0 0;
    width: 100px;
}
.ins-style {
   width: 400px;
}
.ing-style {
    width: 200px;
}
.see-recipe-btn-pos {
    position: relative;
    float: right;
    top: -65px;
    right: 55px;
}
.see-img {
    max-height: 600px;
    max-width: 900px;
}

.modal-img {
    max-height: 200px;
    max-width: 500px;
    position: relative;
}
.nutrition-title {
    font-size: 20px;
    font-family: inherit;
    font-weight: bold;
    line-height: 1.3;
    text-transform: none;
    margin-top: 0;
    margin-bottom: 0;
}

.nutrition-facts {
    border-top: 10px solid;
    border-bottom: 1px solid;
}

    .nutrition-facts,
    .nutrition-facts ul {
        list-style: none;
        margin-top: 0;
        margin-bottom: 0;
        padding-left: 0;
    }

        .nutrition-facts ul {
            flex: 0 0 100%;
        }

            .nutrition-facts ul li {
                margin-left: 1rem;
                margin-right: -0.25rem;
            }

        .nutrition-facts li {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            padding-left: 0.25rem;
            padding-right: 0.25rem;
            border-top: 1px solid;
        }

        .nutrition-facts .nutrition-facts-section {
            border-top-width: 5px;
        }

.nutrition-facts-label {
    font-weight: bold;
}

.nutrition-note {
    padding-top: 5px;
    font-size: 90%;
    font-family: 'Raleway'
}

.nutrition-see {
    padding: 5px;
    border: 1px solid black;
    width: 260px;
}
.pad-left{
    margin-right: 0px;
}

.print-sm-btn{
    display:none;
}

@media only screen and (max-width: 600px) and (min-width: 320px) {
    .print-header{
        top: 90px;
        padding-left: 30px;
    }
    .print-header-buttons{
        display: none;
    }
    .print-section{
        left: 13px;
        top: 110px;
        width: calc(100% - 8%);
    }
    .chk-select-print {
        font-size: 12px;
    }

    .chk-print {
        font-size: 12px;
    }

    .rec-data h4 {
        font-size: 12px;
    }
    .chk-select-all{
        top: 270px;
        right: 15px;
    }
    .chk-wrap{        
        padding: 40px 5px 0 5px;
    }
    .chk-print{
        flex-direction: column;
    }
    .rec-data{
        padding-bottom: 50px;
    }
    .rec-data h4{
        position: absolute;
        left: 10px;
        font-size: 14px;
    }
    .rec-data svg{
        position:relative;
        top: 50px;
    }
    .chk-select-print{
        right: 20px;
    }
    .print-sm-btn{
        display: block;
        position: absolute;
        top: 0px;
        right: 10px;        
    }
    .button-style{
        font-size: 10px;
        padding: 12px;
        border-radius: 20px;
        margin-right: 6px
    }
    .prep-table{
        width: 97%;
    }
    .billing-hist-header{
        width: 100%;
    }
    #report-btn{
        display: none;
    }
    .meal-plan-print {
        padding: 0px 0px 0px 20px;
    }

    .print-shopping {
        padding: 0px 0px 0px 20px;
    }

    .print-recipe-sec {
        padding: 0px 0px 0px 20px;
    }
    .print-prep-guide {
        padding: 0px 0px 0px 20px;
    }
    .recipe-txt-wrap{        
        margin: -40px 0 0 0px;
    }
}

@media only screen and (max-width: 1440px) and (min-width: 1024px) {
    .print-header{
        top: 160px;
        padding-left: 130px;
    }
    .print-section{
        left: 130px;
    }
    .print-header h2{
        font-size: 25px;
    }
    .print-header-buttons{
        top:-50px;
        right: 60px;
    }
    .button-style{
        font-size: 14px;
    }
    .chk-select-print {
        font-size: 12px;
    }

    .chk-print {
        font-size: 12px;
    }

    .rec-data h4 {
        font-size: 20px;
    }
}

@media only screen and (max-width: 2560px) and (min-width: 1921px) {
    .print-section {
        margin-top: 110px;
        left: 230px;
        font-size: 22px;
    }

    .print-bar {
        top: 0px;
    }

    .chk-select-print {
        font-size: 22px;
    }

    .chk-print {
        font-size: 24px;
    }

    .rec-data h4 {
        font-size: 29px;
    }
}