<Project Sdk="Microsoft.NET.Sdk.BlazorWebAssembly">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<RootNamespace>TwentyDishes.Client</RootNamespace>
	</PropertyGroup>

	<ItemGroup>
		<Content Remove="bundleconfig.json" />
	</ItemGroup>

	<ItemGroup>
		<_ContentIncludedByDefault Remove="bundleconfig.json" />
		<_ContentIncludedByDefault Remove="wwwroot\.well-known\microsoft-identity-association.json" />
	</ItemGroup>

	<ItemGroup>
		<None Include="bundleconfig.json" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Blazored.LocalStorage" Version="4.5.0" />
		<PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" Version="9.0.0" />
		<PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Authentication" Version="9.0.0" />
		<PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.DevServer" Version="9.0.0" PrivateAssets="all" />
		<PackageReference Include="Microsoft.Extensions.Http" Version="9.0.0" />
		<PackageReference Include="Syncfusion.Blazor" Version="29.2.5" />
		<PackageReference Include="Syncfusion.Pdf.Net.Core" Version="29.2.5" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Shared\Shared.csproj" />
	</ItemGroup>
</Project>
